<!DOCTYPE html>
<html lang="fr" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ExpenseManager{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="h-full">
    <div class="min-h-full">
        {% if current_user.is_authenticated %}
            {% include 'navbar.html' %}
        {% endif %}
        
        <main>
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pt-4">
                        {% for category, message in messages %}
                            <div class="rounded-md p-4 mb-4 {% if category == 'error' %}bg-red-50 text-red-800{% elif category == 'success' %}bg-green-50 text-green-800{% else %}bg-blue-50 text-blue-800{% endif %}">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas {% if category == 'error' %}fa-exclamation-circle{% elif category == 'success' %}fa-check-circle{% else %}fa-info-circle{% endif %}"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium">{{ message }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            {% block content %}{% endblock %}
        </main>
    </div>
    
    {% block scripts %}{% endblock %}
</body>
</html>
