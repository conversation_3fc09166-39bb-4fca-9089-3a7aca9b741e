from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>ginManager
from flask_migrate import Migrate
from config import Config

db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Initialize extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)

    # Create tables if they don't exist (for development only)
    with app.app_context():
        if app.config.get('FLASK_ENV') != 'production':
            db.create_all()
    
    # Configure login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login_manager.login_message_category = 'info'

    # Add datetime and utility functions to template context
    import datetime
    from app.utils import format_currency

    @app.context_processor
    def inject_datetime():
        return {
            'datetime': datetime,
            'format_currency': format_currency
        }
    
    # Register blueprints
    from app.modules.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.modules.expenses import bp as expenses_bp
    app.register_blueprint(expenses_bp, url_prefix='/expenses')
    
    from app.modules.categories import bp as categories_bp
    app.register_blueprint(categories_bp, url_prefix='/categories')
    
    from app.modules.settings import bp as settings_bp
    app.register_blueprint(settings_bp, url_prefix='/settings')

    from app.modules.reports import bp as reports_bp
    app.register_blueprint(reports_bp, url_prefix='/reports')

    from app.modules.main import bp as main_bp
    app.register_blueprint(main_bp)
    
    return app

from app import models
