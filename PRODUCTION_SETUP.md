# 🚀 Configuration pour la Production

## 1. Tailwind CSS pour la Production

### Problème
Le CDN Tailwind CSS ne doit pas être utilisé en production car :
- Plus lent (fichier volumineux)
- Dépendance externe
- Pas optimisé

### Solution

#### Option A : Tailwind CLI (Recommandée)

1. **Installer Node.js** (si pas déjà fait)
2. **Installer Tailwind CSS** :
```bash
npm install -D tailwindcss
npx tailwindcss init
```

3. **Configurer `tailwind.config.js`** :
```javascript
module.exports = {
  content: ["./app/templates/**/*.html"],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

4. **Créer `app/static/css/input.css`** :
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

5. **Générer le CSS optimisé** :
```bash
npx tailwindcss -i ./app/static/css/input.css -o ./app/static/css/style.css --watch
```

6. **Modifier `base.html`** :
```html
<!-- Remplacer le CDN par : -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
```

#### Option B : Utiliser un CDN optimisé (Plus simple)

Remplacer dans `base.html` :
```html
<!-- Au lieu du CDN play -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Utiliser le CDN optimisé -->
<link href="https://unpkg.com/tailwindcss@^3/dist/tailwind.min.css" rel="stylesheet">
```

## 2. Base de données - Migrations pour la Production

### Pourquoi utiliser les migrations en production ?

- **Sécurité** : Contrôle des changements
- **Versioning** : Historique des modifications
- **Rollback** : Possibilité de revenir en arrière
- **Équipe** : Synchronisation entre développeurs

### Configuration

Le code a été modifié pour utiliser `db.create_all()` seulement en développement :

```python
# Dans app/__init__.py
if app.config.get('FLASK_ENV') != 'production':
    db.create_all()
```

### Workflow recommandé

#### Développement local
```bash
# Les tables se créent automatiquement
python run.py
```

#### Avant déploiement en production
```bash
# Initialiser les migrations (une seule fois)
flask db init

# Créer la migration initiale
flask db migrate -m "Initial migration"

# Appliquer en local pour tester
flask db upgrade
```

#### En production (après déploiement)
```bash
# Via Cloud Shell ou localement avec DATABASE_URL de production
export DATABASE_URL="postgresql://..."
export FLASK_ENV=production

flask db upgrade
```

#### Pour les futures modifications
```bash
# Après modification des modèles
flask db migrate -m "Description du changement"
flask db upgrade
```

## 3. Variables d'environnement pour la Production

### Fichier `.env` de production (ne pas committer)
```bash
# Production settings
FLASK_ENV=production
SECRET_KEY=votre-clé-très-sécurisée-64-caractères-minimum
DATABASE_URL=postgresql://username:password@host:port/database

# Performance
EXPENSES_PER_PAGE=50
MAX_UPLOAD_SIZE=16777216

# Email (optionnel)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre-mot-de-passe-app
```

### Génération de SECRET_KEY sécurisée
```python
import secrets
print(secrets.token_urlsafe(64))
```

## 4. Optimisations de Performance

### Gunicorn pour la production

Ajouter à `requirements.txt` :
```
gunicorn==21.2.0
```

Créer `gunicorn.conf.py` :
```python
bind = "0.0.0.0:8080"
workers = 2
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
```

Modifier `run.py` pour la production :
```python
import os
from app import create_app

app = create_app()

if __name__ == '__main__':
    if os.environ.get('FLASK_ENV') == 'production':
        # En production, utiliser gunicorn
        import gunicorn.app.wsgiapp as wsgi
        wsgi.run()
    else:
        # En développement
        app.run(host='0.0.0.0', port=5000, debug=True)
```

### Commande de démarrage pour Cloud Run
```bash
gunicorn --config gunicorn.conf.py run:app
```

## 5. Sécurité en Production

### Headers de sécurité
Ajouter à `app/__init__.py` :
```python
@app.after_request
def after_request(response):
    if app.config.get('FLASK_ENV') == 'production':
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    return response
```

### Logging en production
```python
import logging
from logging.handlers import RotatingFileHandler

if app.config.get('FLASK_ENV') == 'production':
    if not app.debug:
        handler = RotatingFileHandler('logs/expense_manager.log', maxBytes=10240, backupCount=10)
        handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        handler.setLevel(logging.INFO)
        app.logger.addHandler(handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('ExpenseManager startup')
```

## 6. Checklist avant Production

- [ ] SECRET_KEY forte et unique
- [ ] DATABASE_URL Neon configurée
- [ ] Migrations créées et testées
- [ ] Tailwind CSS optimisé (pas de CDN)
- [ ] Variables d'environnement configurées
- [ ] Tests passés
- [ ] Logs configurés
- [ ] Headers de sécurité activés

## 7. Monitoring et Maintenance

### Surveillance Cloud Run
- Consultez les métriques dans Google Cloud Console
- Configurez des alertes pour les erreurs
- Surveillez l'utilisation des ressources

### Sauvegarde Neon
- Neon fait des sauvegardes automatiques
- Configurez des sauvegardes supplémentaires si nécessaire

### Mises à jour
- Mettez à jour les dépendances régulièrement
- Testez en local avant de déployer
- Utilisez les migrations pour les changements de DB

---

**Suivez ces étapes pour une application production-ready ! 🚀**
