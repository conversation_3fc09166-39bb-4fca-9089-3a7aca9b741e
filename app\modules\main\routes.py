from flask import render_template, redirect, url_for
from flask_login import login_required, current_user
from app.modules.main import bp

@bp.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('expenses.dashboard'))
    return render_template('main/index.html')

@bp.route('/dashboard')
@login_required
def dashboard():
    return redirect(url_for('expenses.dashboard'))
