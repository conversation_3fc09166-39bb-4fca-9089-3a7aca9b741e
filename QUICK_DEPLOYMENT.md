# 🚀 Déploiement Rapide sur Google Cloud Run

## ⚠️ Prérequis Important

**Carte de crédit requise :** Google Cloud exige une carte de crédit même pour l'utilisation gratuite. Cependant, vous ne serez pas facturé si vous restez dans les limites gratuites.

**Limites gratuites Cloud Run :**
- 2 millions de requêtes par mois
- 400 000 Go-secondes de temps de calcul
- 200 000 vCPU-secondes
- 1 Go de trafic sortant par mois

## 📋 Étapes Détaillées

### Étape 1 : Activer les APIs nécessaires

Avant de commencer, vous devez activer les APIs. Cliquez sur le bouton **"Configurer"** mentionné dans l'interface pour activer :
- Cloud Build API
- Artifact Registry API  
- Container Analysis API
- Cloud Run Admin API

### Étape 2 : Configuration du service

Basé sur l'interface que vous avez vue, voici comment remplir chaque champ :

#### **Informations de base**
- **Nom de Service** : `expense-manager` (ou le nom de votre choix)
- **Région** : Choisissez `europe-west1` (Belgique) pour la France

#### **Source et déploiement**
- **Type de source** : Sélectionnez **GitHub**
- **Repository** : Connectez votre repository GitHub
- **Branche** : `main`
- **Type de build** : Laissez sur **"Buildpacks"** (détection automatique)

#### **Authentification**
- Sélectionnez **"Autoriser l'accès public"** pour que votre SaaS soit accessible

#### **Facturation**
- Choisissez **"Basée sur les requêtes"** (plus économique)

#### **Scaling**
- **Autoscaling** : Activé
- **Instances minimales** : `0` (pour économiser)
- **Instances maximales** : `10` (suffisant pour commencer)

#### **Ingress**
- Sélectionnez **"Tous"** pour autoriser l'accès depuis Internet

### Étape 3 : Configuration du conteneur

#### **Port du conteneur**
- Laissez `8080` (déjà configuré dans notre code)

#### **Variables d'environnement**
Cliquez sur **"Variables et secrets"** et ajoutez :

```
DATABASE_URL=postgresql://username:<EMAIL>/neondb
SECRET_KEY=votre-clé-secrète-très-forte-et-aléatoire
FLASK_ENV=production
```

**Comment obtenir DATABASE_URL :**
1. Allez sur [neon.tech](https://neon.tech/)
2. Créez un compte (gratuit)
3. Créez un nouveau projet
4. Dans "Connection Details", copiez l'URL PostgreSQL

**Pour SECRET_KEY :** Générez une clé forte :
```python
import secrets
print(secrets.token_urlsafe(32))
```

#### **Ressources**
- **Mémoire** : `512 MiB` (suffisant)
- **CPU** : `1` vCPU
- **Délai d'expiration** : `300` secondes

#### **Environnement d'exécution**
- Laissez sur **"Par défaut"**

### Étape 4 : Déploiement

1. Cliquez sur **"Créer"**
2. Attendez 5-10 minutes (première compilation)
3. Vous recevrez une URL comme : `https://expense-manager-xxx.run.app`

### Étape 5 : Initialisation de la base de données (IMPORTANT)

Une fois déployé, vous devez initialiser la base de données :

#### Option A : Via Cloud Shell
1. Dans Google Cloud Console, ouvrez **Cloud Shell**
2. Exécutez :
```bash
# Cloner votre repository
git clone https://github.com/votre-username/expense-manager.git
cd expense-manager

# Installer les dépendances
pip install -r requirements.txt

# Configurer les variables
export DATABASE_URL="votre-url-neon-postgresql"
export FLASK_APP=run.py
export FLASK_ENV=production

# Initialiser les migrations
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

#### Option B : Localement (plus simple)
1. Sur votre machine, modifiez temporairement `.env` :
```
DATABASE_URL=postgresql://username:<EMAIL>/neondb
FLASK_ENV=production
```

2. Exécutez :
```bash
flask db init
flask db migrate -m "Initial migration"  
flask db upgrade
```

3. Remettez `.env` en mode développement après

## 🎉 Votre SaaS est en ligne !

Votre application sera accessible à l'URL fournie par Cloud Run.

## 🔧 Mises à jour automatiques

Chaque fois que vous poussez du code sur la branche `main`, Cloud Run redéploiera automatiquement votre application.

## 💰 Surveillance des coûts

- Consultez la **Console de facturation** Google Cloud
- Configurez des **alertes de budget** pour éviter les surprises
- Avec un usage normal, vous devriez rester dans les limites gratuites

## 🐛 Dépannage

### Erreur de build
- Vérifiez les logs dans **Cloud Build**
- Assurez-vous que `requirements.txt` est correct

### Erreur de base de données
- Vérifiez que l'URL Neon est correcte
- Assurez-vous d'avoir exécuté les migrations

### Application inaccessible
- Vérifiez que "Autoriser l'accès public" est activé
- Consultez les logs dans Cloud Run

## 📞 Support

Si vous rencontrez des problèmes :
1. Consultez les logs Cloud Run
2. Vérifiez la documentation Neon
3. Ouvrez une issue sur GitHub

---

**Temps total estimé :** 30-45 minutes pour le premier déploiement
