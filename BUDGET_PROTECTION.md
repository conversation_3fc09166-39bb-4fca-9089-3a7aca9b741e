# 🛡️ Protection Budgétaire Google Cloud

## ⚠️ DANGER : Facturation automatique

**IMPORTANT :** Google Cloud facture automatiquement votre carte de crédit si vous dépassez les limites gratuites. Le service ne s'arrête PAS automatiquement.

## 🚨 Mesures de Protection Obligatoires

### 1. Créer un Budget avec Alertes

#### Étapes détaillées :

1. **Accéder à la facturation**
   - Allez sur [Google Cloud Console](https://console.cloud.google.com/)
   - Menu ☰ > **Facturation** > **Budgets et alertes**

2. **Créer un budget**
   - Cliquez sur **"Créer un budget"**
   - **Nom** : "Protection ExpenseManager"
   - **Projets** : Sélectionnez votre projet
   - **Services** : Tous les services (ou seulement Cloud Run)

3. **Définir le montant**
   - **Type** : Montant spécifié
   - **Montant** : 5€ (ou votre limite maximale acceptable)
   - **Période** : Mensuel

4. **Configurer les alertes**
   - **Seuil 1** : 50% (2,50€) - Email d'avertissement
   - **Seuil 2** : 90% (4,50€) - Email urgent
   - **Seuil 3** : 100% (5€) - Email critique + action

5. **Actions automatiques**
   - Cochez **"Envoyer des notifications par e-mail"**
   - Ajoutez votre email
   - **Important** : Configurez aussi des actions programmatiques (voir section 3)

### 2. Surveillance Quotidienne

#### Dashboard de surveillance
- **URL directe** : https://console.cloud.google.com/billing/reports
- **Fréquence** : Vérifiez CHAQUE JOUR pendant les premiers mois
- **Métriques à surveiller** :
  - Coût total du jour
  - Requêtes Cloud Run
  - Temps de calcul utilisé
  - Trafic sortant

#### Alertes mobiles
- Installez l'app **Google Cloud Console** sur votre téléphone
- Activez les notifications push pour la facturation

### 3. Arrêt Automatique (Avancé)

#### Option A : Cloud Function d'arrêt

Créez une Cloud Function qui arrête votre service si le budget est dépassé :

```python
import functions_framework
from google.cloud import run_v2

@functions_framework.http
def stop_service_on_budget(request):
    """Arrête Cloud Run si budget dépassé"""
    
    # Vérifier que la requête vient de Pub/Sub (budget alert)
    if not request.get_json():
        return 'No JSON payload', 400
    
    # Arrêter le service Cloud Run
    client = run_v2.ServicesClient()
    service_name = "projects/YOUR_PROJECT/locations/europe-west1/services/expense-manager"
    
    # Mettre le service à 0 instance
    service = client.get_service(name=service_name)
    service.spec.template.spec.container_concurrency = 0
    
    operation = client.update_service(service=service)
    
    return f'Service stopped: {operation.name}', 200
```

#### Option B : Script local de surveillance

Créez un script Python qui surveille vos coûts :

```python
import requests
import smtplib
from google.cloud import billing

def check_daily_costs():
    """Vérifie les coûts quotidiens"""
    client = billing.CloudBillingClient()
    
    # Récupérer les coûts du jour
    # (Code à adapter selon vos besoins)
    
    daily_cost = get_daily_cost()  # À implémenter
    
    if daily_cost > 0.50:  # 50 centimes par jour = 15€/mois
        send_alert_email(f"Coût quotidien élevé: {daily_cost}€")
        
    if daily_cost > 1.00:  # 1€ par jour = 30€/mois
        # Arrêter le service ou prendre action
        stop_cloud_run_service()

# Exécuter ce script quotidiennement avec cron
```

### 4. Limites de Sécurité par Code

#### Dans votre application Flask

Ajoutez des limites dans votre code :

```python
# Dans config.py
class ProductionConfig(Config):
    # Limite le nombre de requêtes par utilisateur
    RATELIMIT_STORAGE_URL = "memory://"
    RATELIMIT_DEFAULT = "1000 per day"
    
    # Limite la taille des uploads
    MAX_CONTENT_LENGTH = 1 * 1024 * 1024  # 1MB max
    
    # Désactive certaines fonctionnalités coûteuses en cas de pic
    EMERGENCY_MODE = os.environ.get('EMERGENCY_MODE', False)
```

#### Rate limiting

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per day", "100 per hour"]
)

@app.route('/api/expensive-operation')
@limiter.limit("10 per minute")
def expensive_operation():
    # Opération coûteuse limitée
    pass
```

### 5. Plan d'Urgence

#### Si vous dépassez le budget :

1. **Immédiat** (dans les 5 minutes)
   - Allez sur Cloud Run > votre service
   - Cliquez **"Modifier et déployer une nouvelle révision"**
   - Mettez **"Instances minimales"** à 0
   - Mettez **"Instances maximales"** à 0
   - Sauvegardez

2. **Court terme** (dans l'heure)
   - Analysez les logs pour comprendre le pic
   - Vérifiez s'il y a une attaque DDoS
   - Contactez le support Google Cloud si nécessaire

3. **Moyen terme** (dans la journée)
   - Optimisez votre code
   - Ajoutez du rate limiting
   - Configurez un CDN si nécessaire

### 6. Alternatives Gratuites pour Tests

#### Pour éviter complètement les risques :

1. **Heroku** (gratuit avec limitations)
2. **Railway** (500h gratuites/mois)
3. **Render** (750h gratuites/mois)
4. **Vercel** (gratuit pour projets personnels)

#### Développement local uniquement
- Utilisez SQLite en local
- Testez avec ngrok pour exposer temporairement
- Déployez seulement quand vous êtes prêt

### 7. Checklist de Protection

Avant de déployer, vérifiez :

- [ ] Budget configuré avec alertes à 50%, 90%, 100%
- [ ] Email de notification configuré
- [ ] Application mobile Google Cloud installée
- [ ] Rate limiting activé dans le code
- [ ] Surveillance quotidienne planifiée
- [ ] Plan d'urgence documenté
- [ ] Limites de ressources configurées
- [ ] Tests de charge effectués en local

### 8. Coûts Typiques pour un SaaS de Test

#### Utilisation normale (10-50 utilisateurs test) :
- **Cloud Run** : 0-2€/mois
- **Neon Database** : 0€ (plan gratuit)
- **Total estimé** : 0-2€/mois

#### Utilisation élevée (100-500 utilisateurs) :
- **Cloud Run** : 5-15€/mois
- **Neon Database** : 0-10€/mois
- **Total estimé** : 5-25€/mois

#### En cas d'attaque/bug :
- **Peut atteindre** : 100-1000€/jour ⚠️
- **D'où l'importance** des protections !

---

**🛡️ Suivez ces mesures pour éviter les mauvaises surprises !**
