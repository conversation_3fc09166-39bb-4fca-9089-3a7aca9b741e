# ExpenseManager - SaaS Multi-Tenant de Gestion des Dépenses

ExpenseManager est une application SaaS multi-tenant moderne pour la gestion des dépenses d'entreprise, développée avec Flask et déployable sur Google Cloud Run.

## 🚀 Fonctionnalités

### 🏢 Multi-Tenant
- **Isolation complète** des données par entreprise
- **Gestion des rôles** et permissions granulaires
- **Inscription d'entreprise** avec administrateur initial

### 💰 Gestion des Dépenses
- **Création et modification** de dépenses avec catégorisation
- **Upload de reçus** (images et PDF)
- **Filtres avancés** par date, montant, catégorie, utilisateur
- **Recherche textuelle** dans les titres et descriptions

### 📊 Catégories
- **Catégories personnalisables** avec couleurs
- **Catégories par défaut** lors de la création d'entreprise
- **Gestion du statut** actif/inactif

### 📈 Rapports et Analyses
- **Tableau de bord** avec métriques clés
- **Graphiques interactifs** (Chart.js)
- **Analyse mensuelle** et par catégorie
- **Comparaison année sur année**
- **Top utilisateurs** et catégories

### 👥 Gestion des Utilisateurs
- **Système de rôles** (Admin, Manager, Employee, Viewer)
- **Permissions granulaires** par fonctionnalité
- **Profils utilisateur** complets

## 🛠️ Stack Technologique

### Backend
- **Python 3.11** avec Flask
- **SQLAlchemy** pour l'ORM
- **Flask-Login** pour l'authentification
- **Flask-Migrate** pour les migrations
- **WTForms** pour les formulaires

### Frontend
- **HTML5** avec templates Jinja2
- **Tailwind CSS** pour le styling
- **Chart.js** pour les graphiques
- **Font Awesome** pour les icônes
- **JavaScript minimal** (uniquement quand nécessaire)

### Base de Données
- **SQLite** pour le développement
- **PostgreSQL** (Neon) pour la production
- **Indexation optimisée** pour les performances

### Déploiement
- **Google Cloud Run** pour l'hébergement (déploiement direct depuis GitHub)
- **Neon PostgreSQL** pour la base de données production

## 📁 Architecture du Projet

```
expense_manager/
├── app/
│   ├── __init__.py                 # Factory Flask
│   ├── models.py                   # Modèles SQLAlchemy
│   ├── decorators.py              # Décorateurs de sécurité
│   ├── utils.py                   # Fonctions utilitaires
│   ├── modules/                   # Modules fonctionnels
│   │   ├── auth/                  # Authentification
│   │   ├── expenses/              # Gestion des dépenses
│   │   ├── categories/            # Gestion des catégories
│   │   ├── reports/               # Rapports et analyses
│   │   ├── settings/              # Paramètres
│   │   └── main/                  # Pages principales
│   └── templates/                 # Templates HTML
│       ├── base.html              # Template de base
│       ├── navbar.html            # Navigation
│       ├── auth/                  # Templates d'auth
│       ├── expenses/              # Templates de dépenses
│       ├── categories/            # Templates de catégories
│       ├── reports/               # Templates de rapports
│       └── settings/              # Templates de paramètres
├── migrations/                    # Migrations de base de données
├── instance/                      # Données d'instance
├── .github/workflows/             # GitHub Actions
├── requirements.txt               # Dépendances Python
├── config.py                      # Configuration
├── run.py                         # Point d'entrée
├── Dockerfile                     # Configuration Docker
├── docker-compose.yml             # Développement local
├── cloudbuild.yaml               # Configuration Cloud Build
└── DEPLOYMENT.md                  # Guide de déploiement
```

## 🚀 Installation et Développement Local

### Prérequis
- Python 3.11+
- Git
- Docker (optionnel)

### Installation
```bash
# Cloner le repository
git clone https://github.com/votre-username/expense-manager.git
cd expense-manager

# Créer un environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows

# Installer les dépendances
pip install -r requirements.txt

# Configurer les variables d'environnement
cp .env.example .env
# Éditer .env avec vos valeurs

# Lancer l'application (les tables se créent automatiquement)
python run.py
```

**Note :** Les tables de base de données se créent automatiquement au premier lancement grâce à `db.create_all()`.

### Pour les mises à jour de base de données (optionnel)

Si vous modifiez les modèles et voulez gérer les migrations :

```bash
# Initialiser les migrations (une seule fois)
flask db init

# Créer une migration après modification des modèles
flask db migrate -m "Description des changements"

# Appliquer les migrations
flask db upgrade
```

## 🔧 Configuration

### Variables d'environnement

Créez un fichier `.env` basé sur `.env.example` :

```bash
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///expense_manager.db
# Pour PostgreSQL: postgresql://username:password@host:port/database

# Application Settings
EXPENSES_PER_PAGE=20
MAX_UPLOAD_SIZE=16777216

# Email Configuration (optionnel)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## 🏗️ Modèles de Données

### Company (Multi-Tenant)
- Informations de l'entreprise
- Isolation des données par tenant

### User
- Utilisateurs avec rôles
- Authentification et permissions

### Role
- Système de rôles granulaire
- Permissions par fonctionnalité

### Category
- Catégories de dépenses
- Couleurs personnalisables

### Expense
- Dépenses avec reçus
- Filtrage et recherche optimisés

## 🔐 Sécurité

### Multi-Tenant
- **Isolation complète** des données par entreprise
- **Middleware de vérification** de tenant
- **Requêtes filtrées** automatiquement

### Authentification
- **Mots de passe hachés** avec Werkzeug
- **Sessions sécurisées** avec Flask-Login
- **Protection CSRF** avec Flask-WTF

### Permissions
- **Système de rôles** granulaire
- **Décorateurs de permission** sur les routes
- **Vérifications côté client et serveur**

## 📊 Fonctionnalités Avancées

### Rapports
- **Graphiques interactifs** avec Chart.js
- **Filtres par période** et catégorie
- **Exports** (à implémenter)
- **APIs REST** pour les données

### Upload de Fichiers
- **Support multi-format** (JPG, PNG, PDF)
- **Stockage sécurisé** avec noms uniques
- **Limitation de taille** configurable

### Performance
- **Indexation optimisée** des requêtes
- **Pagination** des listes
- **Cache** des requêtes fréquentes

## 🚀 Déploiement sur Google Cloud Run

### Méthode Simple (Recommandée)

**Étape 1 : Préparer la base de données**
1. Créez un compte sur [Neon](https://neon.tech/)
2. Créez une nouvelle base de données PostgreSQL
3. Copiez l'URL de connexion

**Étape 2 : Déployer sur Cloud Run**
1. Allez sur [Google Cloud Console](https://console.cloud.google.com/)
2. Naviguez vers **Cloud Run**
3. Cliquez sur **"Créer un service"**
4. Sélectionnez **"Déployer en continu depuis un dépôt"**
5. Connectez votre repository GitHub
6. Configurez les variables d'environnement :
   - `DATABASE_URL` : votre URL Neon PostgreSQL
   - `SECRET_KEY` : une clé secrète forte
7. Cliquez sur **"Créer"**

**C'est tout !** Votre SaaS sera automatiquement déployé et mis à jour à chaque push sur main.

### Déploiement avancé
Consultez [DEPLOYMENT.md](DEPLOYMENT.md) pour plus d'options.

## 🧪 Tests

```bash
# Lancer les tests
python -m pytest tests/

# Avec coverage
python -m pytest --cov=app tests/
```

## 🤝 Contribution

1. **Fork** le projet
2. **Créez** une branche feature (`git checkout -b feature/AmazingFeature`)
3. **Committez** vos changements (`git commit -m 'Add some AmazingFeature'`)
4. **Poussez** vers la branche (`git push origin feature/AmazingFeature`)
5. **Ouvrez** une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](LICENSE) pour plus de détails.

## 📞 Support

- **Documentation** : Consultez ce README et DEPLOYMENT.md
- **Issues** : Ouvrez une issue sur GitHub
- **Email** : <EMAIL>

## 🗺️ Roadmap

### Version 1.1
- [ ] Export PDF des rapports
- [ ] Notifications email
- [ ] API REST complète
- [ ] Application mobile

### Version 1.2
- [ ] Intégration bancaire
- [ ] OCR pour les reçus
- [ ] Workflows d'approbation
- [ ] Multi-devises

### Version 2.0
- [ ] Intelligence artificielle
- [ ] Prédictions de dépenses
- [ ] Intégrations tierces
- [ ] Mode hors-ligne

---

**ExpenseManager** - Simplifiez la gestion de vos dépenses d'entreprise 💼✨
