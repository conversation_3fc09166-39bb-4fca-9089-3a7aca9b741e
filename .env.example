# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
# For development (SQLite)
DATABASE_URL=sqlite:///expense_manager.db

# For production (Neon/PostgreSQL)
# DATABASE_URL=postgresql://username:password@host:port/database

# Email Configuration (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Application Settings
EXPENSES_PER_PAGE=20
MAX_UPLOAD_SIZE=16777216
