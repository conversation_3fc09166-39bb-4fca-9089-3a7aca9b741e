# Guide de déploiement - ExpenseManager

Ce guide vous explique comment déployer ExpenseManager sur Google Cloud Run avec une base de données Neon PostgreSQL.

## 🚀 Méthode Simple (Recommandée)

Cette méthode utilise le déploiement direct depuis GitHub sans Docker ni configuration complexe.

### Étapes rapides

1. **Créer une base de données Neon**
   - Allez sur [neon.tech](https://neon.tech/)
   - Créez un compte et un nouveau projet
   - Copiez l'URL de connexion PostgreSQL

2. **Déployer sur Cloud Run**
   - Allez sur [Google Cloud Console](https://console.cloud.google.com/)
   - Naviguez vers **Cloud Run** > **Créer un service**
   - Sélectionnez **"Déployer en continu depuis un dépôt"**
   - Connectez votre repository GitHub
   - Branche : `main`
   - Type de build : `Buildpacks`
   - Variables d'environnement :
     ```
     DATABASE_URL=postgresql://username:password@host:port/database
     SECRET_KEY=votre-clé-secrète-forte
     FLASK_ENV=production
     ```
   - Cliquez sur **"Créer"**

3. **Accéder à votre application**
   - Cloud Run vous donnera une URL (ex: `https://expense-manager-xxx.run.app`)
   - Votre SaaS est maintenant en ligne !

**Avantages :**
- ✅ Pas de Docker à configurer
- ✅ Déploiement automatique à chaque push
- ✅ Scaling automatique
- ✅ HTTPS inclus

---

## 🔧 Méthode Avancée (Optionnelle)

Pour plus de contrôle sur le déploiement.

## Prérequis

1. **Compte Google Cloud Platform (GCP)**
   - Créez un compte sur [Google Cloud Console](https://console.cloud.google.com/)
   - Créez un nouveau projet ou utilisez un projet existant
   - Activez la facturation sur votre projet

2. **Compte Neon Database**
   - Créez un compte sur [Neon](https://neon.tech/)
   - Créez une nouvelle base de données PostgreSQL

3. **Compte GitHub**
   - Pour le déploiement automatique via GitHub Actions

## Étape 1: Configuration de la base de données Neon

1. **Créer une base de données Neon**
   ```
   - Allez sur https://neon.tech/
   - Créez un compte et connectez-vous
   - Cliquez sur "Create Project"
   - Choisissez un nom pour votre projet (ex: expense-manager)
   - Sélectionnez la région la plus proche (Europe West pour la France)
   - Cliquez sur "Create Project"
   ```

2. **Récupérer l'URL de connexion**
   ```
   - Dans le dashboard Neon, allez dans "Connection Details"
   - Copiez l'URL de connexion qui ressemble à :
   postgresql://username:<EMAIL>/neondb
   ```

## Étape 2: Configuration de Google Cloud Platform

1. **Activer les APIs nécessaires**
   ```bash
   gcloud services enable cloudbuild.googleapis.com
   gcloud services enable run.googleapis.com
   gcloud services enable containerregistry.googleapis.com
   ```

2. **Créer un compte de service**
   ```bash
   # Créer le compte de service
   gcloud iam service-accounts create expense-manager-deploy \
     --description="Service account for ExpenseManager deployment" \
     --display-name="ExpenseManager Deploy"

   # Attribuer les rôles nécessaires
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:expense-manager-deploy@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/cloudbuild.builds.editor"

   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:expense-manager-deploy@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/run.admin"

   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:expense-manager-deploy@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/storage.admin"

   # Créer et télécharger la clé
   gcloud iam service-accounts keys create key.json \
     --iam-account=expense-manager-deploy@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

## Étape 3: Configuration du repository GitHub

1. **Forker ou cloner le repository**
   ```bash
   git clone https://github.com/votre-username/expense-manager.git
   cd expense-manager
   ```

2. **Configurer les secrets GitHub**
   - Allez dans Settings > Secrets and variables > Actions
   - Ajoutez les secrets suivants :

   ```
   GCP_PROJECT_ID: votre-project-id-gcp
   GCP_SA_KEY: contenu-du-fichier-key.json (copier tout le contenu JSON)
   DATABASE_URL: postgresql://username:<EMAIL>/neondb
   SECRET_KEY: une-clé-secrète-forte-et-aléatoire
   ```

## Étape 4: Déploiement manuel (première fois)

1. **Installer Google Cloud SDK**
   ```bash
   # Sur Ubuntu/Debian
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   gcloud init

   # Ou téléchargez depuis https://cloud.google.com/sdk/docs/install
   ```

2. **Authentification**
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

3. **Build et déploiement**
   ```bash
   # Build de l'image Docker
   docker build -t gcr.io/YOUR_PROJECT_ID/expense-manager .

   # Push vers Container Registry
   docker push gcr.io/YOUR_PROJECT_ID/expense-manager

   # Déploiement sur Cloud Run
   gcloud run deploy expense-manager \
     --image gcr.io/YOUR_PROJECT_ID/expense-manager \
     --region europe-west1 \
     --platform managed \
     --allow-unauthenticated \
     --set-env-vars DATABASE_URL="postgresql://username:<EMAIL>/neondb",SECRET_KEY="votre-clé-secrète" \
     --memory 512Mi \
     --cpu 1 \
     --max-instances 10 \
     --port 8080
   ```

## Étape 5: Initialisation de la base de données

1. **Accéder au Cloud Shell ou utiliser votre terminal local**

2. **Exécuter les migrations**
   ```bash
   # Si vous avez accès à l'application déployée
   gcloud run services proxy expense-manager --port=8080 --region=europe-west1

   # Dans un autre terminal
   export DATABASE_URL="postgresql://username:<EMAIL>/neondb"
   flask db upgrade
   ```

## Étape 6: Configuration du déploiement automatique

Une fois les secrets GitHub configurés, chaque push sur la branche `main` déclenchera automatiquement :

1. **Tests automatiques** (si configurés)
2. **Build de l'image Docker**
3. **Déploiement sur Cloud Run**

## Étape 7: Accéder à votre application

1. **Récupérer l'URL de votre service**
   ```bash
   gcloud run services describe expense-manager --region europe-west1 --format 'value(status.url)'
   ```

2. **Ouvrir l'application dans votre navigateur**
   - L'URL ressemblera à : `https://expense-manager-xxx-ew.a.run.app`

## Configuration des variables d'environnement

Pour la production, assurez-vous de configurer :

```bash
# Variables obligatoires
DATABASE_URL=postgresql://username:password@host:port/database
SECRET_KEY=une-clé-très-sécurisée-et-aléatoire

# Variables optionnelles
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=votre-mot-de-passe-app
```

## Surveillance et logs

1. **Voir les logs**
   ```bash
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=expense-manager" --limit 50 --format json
   ```

2. **Monitoring**
   - Allez dans Google Cloud Console > Cloud Run > expense-manager
   - Consultez les métriques de performance et d'utilisation

## Dépannage

### Problèmes courants

1. **Erreur de connexion à la base de données**
   - Vérifiez que l'URL de la base de données est correcte
   - Assurez-vous que Neon autorise les connexions externes

2. **Erreur de déploiement**
   - Vérifiez les logs de Cloud Build
   - Assurez-vous que tous les secrets GitHub sont configurés

3. **Application inaccessible**
   - Vérifiez que `--allow-unauthenticated` est configuré
   - Vérifiez les règles de pare-feu si nécessaire

### Commandes utiles

```bash
# Voir les services Cloud Run
gcloud run services list

# Voir les révisions
gcloud run revisions list --service expense-manager --region europe-west1

# Voir les logs en temps réel
gcloud run services logs tail expense-manager --region europe-west1

# Mettre à jour les variables d'environnement
gcloud run services update expense-manager \
  --region europe-west1 \
  --set-env-vars NEW_VAR=value
```

## Sécurité

1. **Utilisez HTTPS** (automatiquement fourni par Cloud Run)
2. **Configurez des secrets forts** pour SECRET_KEY
3. **Limitez l'accès** à votre base de données Neon
4. **Surveillez les logs** régulièrement
5. **Mettez à jour** les dépendances régulièrement

## Coûts

- **Cloud Run** : Facturation à l'utilisation (gratuit jusqu'à 2 millions de requêtes/mois)
- **Neon Database** : Plan gratuit disponible avec limitations
- **Container Registry** : Stockage des images Docker

Pour plus d'informations, consultez la documentation officielle de [Google Cloud Run](https://cloud.google.com/run/docs) et [Neon](https://neon.tech/docs).
